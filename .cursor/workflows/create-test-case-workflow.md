# Quy trình Tạo Test Case

## Tổng quan quy trình
Quy trình này bao gồm 3 bư<PERSON><PERSON> ch<PERSON>h:
1. <PERSON><PERSON><PERSON> định domain URL mục tiêu cho test case
2. <PERSON><PERSON><PERSON> hiểu issue và codebase, tạo test case theo rules và template
3. <PERSON><PERSON><PERSON> thiện và comment (n<PERSON>u cần)

## Mục tiêu
Quy trình này định nghĩa các bước tạo test case chất lượ<PERSON> cao, dựa trên thông tin từ GitHub issue và codebase, nhằm đảm bảo độ phủ kiểm thử đầy đủ và chính xác, tuân thủ **NGHIÊM NGẶT** theo các quy tắc đã đề ra.

## Cách sử dụng
1. B<PERSON>t đầu quy trình: "Thực hiện bước 1 của create-test-case-workflow cho issue #XXX"
2. <PERSON><PERSON><PERSON><PERSON> sang bước tiếp theo: "Tiế<PERSON> tục với bước 2 của create-test-case-workflow cho issue #XXX"
3. <PERSON><PERSON><PERSON> thi<PERSON>n quy trình: "Tiếp tục với bước 3 của create-test-case-workflow cho issue #XXX"

## Các bước thực hiện

### 1. Xác định domain URL mục tiêu cho test case
**Lệnh mẫu:** "Thực hiện bước 1 của create-test-case-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:
- Yêu cầu user xác định rõ domain URL mục tiêu để viết test case (ví dụ: https://kuruma-mb5.zigexn.vn/usedcar/, https://v2-vn.tcv-dev.com/, v.v.)
- Nếu user chưa xác định được domain URL cụ thể, tôi sẽ sử dụng "{host}" làm domain placeholder (ví dụ: {host}/used_car/all/all/)
- Xác nhận domain URL với user trước khi tiếp tục với bước tiếp theo
- **Lưu trữ domain URL** để sử dụng nhất quán trong toàn bộ quy trình

### 2. Đọc hiểu issue và codebase, tạo test case
**Lệnh mẫu:** "Tiếp tục với bước 2 của create-test-case-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:

#### 2.1. Thu thập và phân tích thông tin
- Thu thập thông tin từ GitHub issue và các comment trong issue, đặc biệt là nội dung `Phân Tích Yêu Cầu`
- Nếu không có comment `Phân Tích Yêu Cầu` thì phân tích toàn bộ nội dung issue, các comment và nội dung của các issue liên quan
- Phân tích codebase liên quan đến requirement
- **Xử lý edge cases:**
  - Nếu issue thiếu thông tin: Yêu cầu user bổ sung hoặc tìm kiếm trong codebase
  - Nếu requirement không rõ ràng: Đặt câu hỏi cụ thể để làm rõ
  - Nếu có nhiều interpretation: Liệt kê các khả năng và yêu cầu user chọn

#### 2.2. Tóm tắt và xác nhận
- Tóm tắt phân tích và xác nhận với user:
  - Trình bày tóm tắt các hiểu biết về issue
  - **Highlight các assumption** đã đưa ra
  - Xác nhận tính chính xác của phân tích
  - **Ước lượng số lượng test case** sẽ tạo (theo complexity)

#### 2.3. Tạo test case
- Dựa vào các scenarios trong phần `Acceptance Criteria` (định dạng Gherkin) từ comment `Phân Tích Yêu Cầu` trên GitHub issue để tạo ra các test case tương ứng
- Mỗi scenario Gherkin sẽ được chuyển đổi thành một hoặc nhiều test case cụ thể
- Đảm bảo test case bao phủ tất cả các Given-When-Then scenarios đã được định nghĩa
- Phân tích vấn đề cần kiểm thử theo **độ ưu tiên** trong rules
- Tạo các test case **PHẢI TUÂN THỦ NGHIÊM NGẶT** theo rules trong file [create-test-case-rules](mdc:.cursor/rules/test-case-rules/create-test-case-rules.mdc)
- Viết chi tiết test case **PHẢI THEO CHÍNH XÁC** template [test-case](mdc:.cursor/templates/test-case-template.md) **KHÔNG ĐƯỢC BỎ QUA BẤT KỲ PHẦN NÀO**
- Đảm bảo sử dụng domain URL đã xác định ở bước 1 trong tất cả các test case
- **Áp dụng validation checklist** từ rules trước khi hoàn thành

#### 2.4. Trình bày kết quả
- Phản hồi lại user và liệt kê trình bày các test case sẽ định tạo và xác nhận với user (**KHÔNG tạo file mới**)
- **Bao gồm thống kê:**
  - Tổng số test case
  - Phân bổ theo loại (Positive/Negative/Boundary)
  - Độ phủ chức năng

### 3. Hoàn thiện và comment
**Lệnh mẫu:** "Tiếp tục với bước 3 của create-test-case-workflow cho issue #XXX"

Khi được yêu cầu, tôi sẽ:

#### 3.1. Review và điều chỉnh
- Hỏi user xem có muốn điều chỉnh test case không
- Tiến hành điều chỉnh nếu cần thiết, **LUÔN ĐẢM BẢO TUÂN THỦ** rules và template
- **Chạy final validation checklist:**
  - [ ] Tất cả test case có ID và prefix đúng format
  - [ ] URL/API endpoints sử dụng domain đã xác định
  - [ ] Expected results có thể đo lường được
  - [ ] Coverage đầy đủ cho requirement
  - [ ] Test cases độc lập, không phụ thuộc lẫn nhau

#### 3.2. Tạo file và comment
- **Tạo file test case** với tên format: `test-cases-issue-{issue_number}.md`
- Hỏi user xem có muốn comment test case lên GitHub issue không
- Thực hiện theo lựa chọn của user (chỉ comment khi được phép rõ ràng)

#### 3.3. Kết thúc và chuyển tiếp
- Sau khi hoàn thành, hỏi user: "Bạn có muốn tiếp tục với auto-testing-workflow để thực hiện kiểm thử tự động cho issue #XXX không?"
- **Cung cấp summary report:**
  - Số lượng test case đã tạo
  - Thời gian ước tính để execute
  - Các dependency cần chuẩn bị

## Lưu ý quan trọng
- **BẮT BUỘC**: Tất cả test case **PHẢI TUÂN THỦ NGHIÊM NGẶT** theo rules từ file [create-test-case-rules](mdc:.cursor/rules/test-case-rules/create-test-case-rules.mdc)
- **BẮT BUỘC**: Tất cả test case **PHẢI THEO ĐÚNG FORMAT** của template [test-case](mdc:.cursor/templates/test-case-template.md) **KHÔNG ĐƯỢC THAY ĐỔI CẤU TRÚC**
- **BẮT BUỘC**: Sử dụng domain URL đã xác định ở bước 1 trong tất cả các test case
- **BẮT BUỘC**: Chạy validation checklist trước khi hoàn thành mỗi bước
- Chỉ comment lên GitHub khi được cho phép rõ ràng
- Đảm bảo test case bao quát đầy đủ các tình huống cần kiểm thử
- **QUAN TRỌNG**: Khi không chắc chắn, **PHẢI THAM KHẢO** rules và template trước khi tiếp tục
- **LUÔN TẠO FILE** test case ở bước 3 để lưu trữ kết quả

## Troubleshooting
### Khi gặp vấn đề:
1. **Issue thiếu thông tin**: Tìm kiếm trong codebase hoặc yêu cầu user bổ sung
2. **Requirement mơ hồ**: Đặt câu hỏi cụ thể để làm rõ
3. **Quá nhiều test case**: Ưu tiên theo rules (Positive → Negative → Boundary)
4. **Không tìm thấy endpoint**: Tìm kiếm trong routes hoặc API documentation
5. **Template không phù hợp**: Tuân thủ template nhưng adapt cho context cụ thể
